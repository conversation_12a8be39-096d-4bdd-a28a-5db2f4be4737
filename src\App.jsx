import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Login from './pages/Login/Login.jsx'
import Register from './pages/Register/Signup.jsx'
import Dashboard from './pages/Dashboard/Dashboard.jsx'
import ProtectedRoute from './pages/Routes/ProtectedRoutes/ProtectedRoute.jsx'
import NetworkDetector from './components/NetworkDetector.jsx'

function App() {
  return (
    <Router>
      <NetworkDetector>
        <div className="min-h-screen bg-[#F8F9FA]">
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />

            {/* Protected Dashboard Routes */}
            <Route path="/dashboard/*" element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />

            {/* Default route: show login page */}
            <Route path="/" element={<Login />} />
          </Routes>
        </div>
      </NetworkDetector>
    </Router>
  )
}

export default App
